import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/routes/app_routes.dart';
import '../../../core/widgets/responsive/responsive_padding.dart';
import '../models/class_model.dart';
import '../models/activity_model.dart';
import '../controllers/classroom_controller.dart';
import '../controllers/activity_controller.dart';

import '../widgets/classroom_header.dart';
import '../widgets/navigation_grid.dart';
import '../widgets/recent_activity_feed.dart';
import '../enums/activity_type.dart';

/// Screen displaying detailed information about a specific classroom
class ClassroomDetailScreen extends ConsumerWidget {
  /// ID of the classroom to display
  final String classroomId;

  const ClassroomDetailScreen({super.key, required this.classroomId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final classroomAsync = ref.watch(classroomDetailProvider(classroomId));
    final activitiesAsync = ref.watch(
      recentClassroomActivitiesProvider(classroomId),
    );

    return Scaffold(
      appBar: AppBar(
        title: classroomAsync.when(
          data: (classroom) => Text(
            classroom?.name ?? 'Classroom',
            style: theme.textTheme.titleLarge,
          ),
          loading: () => Text('Loading...', style: theme.textTheme.titleLarge),
          error: (_, __) => Text('Error', style: theme.textTheme.titleLarge),
        ),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Symbols.more_vert),
            onPressed: () {
              // TODO: Show classroom options menu
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Classroom options coming soon')),
              );
            },
          ),
        ],
      ),
      body: classroomAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => _ErrorState(
          error: error.toString(),
          onRetry: () => ref.invalidate(classroomDetailProvider(classroomId)),
        ),
        data: (classroom) {
          if (classroom == null) {
            return _NotFoundState();
          }

          return ResponsivePadding(
            mobile: EdgeInsets.all(16.w),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Classroom header with info
                  ClassroomHeader(classroom: classroom),

                  SizedBox(height: 24.h),

                  // Navigation grid (2x3)
                  NavigationGrid(
                    classroomId: classroomId,
                    onNavigate: (action) =>
                        _handleNavigation(context, ref, action, classroom),
                  ),

                  SizedBox(height: 24.h),

                  // Recent activity section
                  _RecentActivitySection(
                    classroomId: classroomId,
                    activitiesAsync: activitiesAsync,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Handle navigation to different sections
  void _handleNavigation(
    BuildContext context,
    WidgetRef ref,
    String section,
    ClassModel classroom,
  ) {
    switch (section) {
      case 'homework':
        // Navigate to homework list with classroom filter
        context.pushNamed(
          RouteNames.homeworkList,
          queryParameters: {'classroomId': classroomId},
        );
        break;
      case 'notes':
        // TODO: Navigate to notes screen
        _showComingSoonMessage(context, 'Notes');
        break;
      case 'quizzes':
        // TODO: Navigate to quizzes screen
        _showComingSoonMessage(context, 'Quizzes');
        break;
      case 'notices':
        // TODO: Navigate to notices screen
        _showComingSoonMessage(context, 'Notices');
        break;
      case 'discussions':
        context.pushNamed(
          RouteNames.classroomDiscussion,
          pathParameters: {'id': classroomId},
        );
        break;
      case 'attendance':
        // TODO: Navigate to attendance screen
        _showComingSoonMessage(context, 'Attendance');
        break;
      case 'digital_library':
        context.pushNamed(
          RouteNames.classroomResources,
          pathParameters: {'id': classroomId},
        );
        break;
    }
  }

  /// Show coming soon message for unimplemented features
  void _showComingSoonMessage(BuildContext context, String feature) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('$feature feature coming soon')));
  }
}

/// Error state widget for classroom detail
class _ErrorState extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const _ErrorState({required this.error, required this.onRetry});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.error_outline,
            size: 64.sp,
            color: theme.colorScheme.error,
          ),
          SizedBox(height: 16.h),
          Text(
            'Something went wrong',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            error,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(onPressed: onRetry, child: const Text('Retry')),
        ],
      ),
    );
  }
}

/// Not found state widget for classroom detail
class _NotFoundState extends StatelessWidget {
  const _NotFoundState();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.school,
            size: 64.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            'Classroom not found',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'The classroom you\'re looking for doesn\'t exist or has been removed.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Recent activity section widget
class _RecentActivitySection extends StatelessWidget {
  final String classroomId;
  final AsyncValue<List<ActivityModel>> activitiesAsync;

  const _RecentActivitySection({
    required this.classroomId,
    required this.activitiesAsync,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Activity',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            activitiesAsync.when(
              data: (activities) => activities.isNotEmpty
                  ? TextButton(
                      onPressed: () => _navigateToActivityFeed(context),
                      child: const Text('View All'),
                    )
                  : const SizedBox.shrink(),
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),
          ],
        ),

        SizedBox(height: 12.h),

        // Activities content
        activitiesAsync.when(
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => Text(
            'Failed to load activities: $error',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          data: (activities) {
            if (activities.isEmpty) {
              return Text(
                'No recent activity',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              );
            }

            return RecentActivityFeed(
              activities: activities,
              onActivityTap: (activity) =>
                  _handleActivityTap(context, activity),
            );
          },
        ),
      ],
    );
  }

  void _navigateToActivityFeed(BuildContext context) {
    context.pushNamed(
      RouteNames.activityFeed,
      pathParameters: {'id': classroomId},
    );
  }

  void _handleActivityTap(BuildContext context, ActivityModel activity) {
    if (!activity.type.isClickable) return;

    switch (activity.type) {
      case ActivityType.homework:
        if (activity.referenceId != null) {
          context.pushNamed(
            RouteNames.homeworkDetail,
            pathParameters: {'id': activity.referenceId!},
          );
        }
        break;
      case ActivityType.announcement:
        // Show announcement details in a dialog or bottom sheet
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(activity.title),
            content: Text(activity.description ?? 'No description'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
        break;
      default:
        // For other types, show coming soon message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${activity.type.label} details coming soon')),
        );
    }
  }
}
