import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/routes/app_routes.dart';
import '../../../core/widgets/responsive/responsive_padding.dart';
import '../enums/classroom_type.dart';
import '../models/class_model.dart';
import '../controllers/classroom_controller.dart';
import '../widgets/classroom_card.dart';
import '../widgets/classroom_filter_chips.dart';
import '../widgets/classroom_search_bar.dart';

/// Screen displaying all enrolled classrooms with filtering and search functionality
class ClassroomsListScreen extends ConsumerStatefulWidget {
  const ClassroomsListScreen({super.key});

  @override
  ConsumerState<ClassroomsListScreen> createState() =>
      _ClassroomsListScreenState();
}

class _ClassroomsListScreenState extends ConsumerState<ClassroomsListScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final classroomsAsync = ref.watch(filteredClassroomsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('My Classrooms', style: theme.textTheme.titleLarge),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar
            ClassroomSearchBar(
              onSearchChanged: (query) {
                ref.read(classroomSearchProvider.notifier).state = query;
              },
            ),

            SizedBox(height: 16.h),

            // Filter chips
            ClassroomFilterChips(
              selectedFilter: ref.watch(classroomFilterProvider),
              onFilterChanged: (filter) {
                ref.read(classroomFilterProvider.notifier).state = filter;
              },
            ),

            SizedBox(height: 16.h),

            // Results and content
            Expanded(
              child: classroomsAsync.when(
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stackTrace) => _ErrorState(
                  error: error.toString(),
                  onRetry: () {
                    ref.invalidate(filteredClassroomsProvider);
                    ref.invalidate(userClassroomsProvider);
                  },
                ),
                data: (classrooms) {
                  if (classrooms.isEmpty) {
                    return _EmptyState(
                      searchQuery: ref.watch(classroomSearchProvider),
                      selectedFilter: ref.watch(classroomFilterProvider),
                    );
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Results count
                      Text(
                        '${classrooms.length} classroom${classrooms.length != 1 ? 's' : ''}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),

                      SizedBox(height: 12.h),

                      // Classroom list
                      Expanded(
                        child: ListView.separated(
                          itemCount: classrooms.length,
                          separatorBuilder: (context, index) =>
                              SizedBox(height: 12.h),
                          itemBuilder: (context, index) {
                            final classroom = classrooms[index];
                            return ClassroomCard(
                              classroom: classroom,
                              onTap: () =>
                                  _navigateToClassroomDetail(classroom.id),
                              onMenuAction: (action) =>
                                  _handleMenuAction(action, classroom),
                            );
                          },
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to classroom detail screen
  void _navigateToClassroomDetail(String classroomId) {
    context.pushNamed(
      RouteNames.classroomDetail,
      pathParameters: {'id': classroomId},
    );
  }

  /// Handle menu actions on classroom cards
  void _handleMenuAction(String action, ClassModel classroom) {
    switch (action) {
      case 'archive':
        // TODO: Implement archive functionality
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Archived ${classroom.name}')));
        break;
      case 'leave':
        // TODO: Implement leave classroom functionality
        _showLeaveClassroomDialog(classroom);
        break;
    }
  }

  /// Show leave classroom confirmation dialog
  void _showLeaveClassroomDialog(ClassModel classroom) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Classroom'),
        content: Text('Are you sure you want to leave ${classroom.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement actual leave logic
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('Left ${classroom.name}')));
            },
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }

  /// Show delete classroom confirmation dialog
}

/// Error state widget for classroom list
class _ErrorState extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const _ErrorState({required this.error, required this.onRetry});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.error_outline,
            size: 64.sp,
            color: theme.colorScheme.error,
          ),
          SizedBox(height: 16.h),
          Text(
            'Something went wrong',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            error,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(onPressed: onRetry, child: const Text('Retry')),
        ],
      ),
    );
  }
}

/// Empty state widget for classroom list
class _EmptyState extends StatelessWidget {
  final String searchQuery;
  final ClassroomType? selectedFilter;

  const _EmptyState({required this.searchQuery, this.selectedFilter});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasFilters = searchQuery.isNotEmpty || selectedFilter != null;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            hasFilters ? Symbols.search_off : Symbols.school,
            size: 64.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            hasFilters ? 'No classrooms found' : 'No classrooms yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            hasFilters
                ? 'Try adjusting your search or filters'
                : 'Create your first classroom to get started',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
