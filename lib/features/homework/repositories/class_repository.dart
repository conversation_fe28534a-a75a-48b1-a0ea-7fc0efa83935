import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../classroom/models/class_model.dart';

/// Repository for managing class data with Firebase Firestore
class ClassRepository {
  static final ClassRepository _instance = ClassRepository._internal();
  factory ClassRepository() => _instance;
  ClassRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection name
  static const String _classesCollection = 'classrooms';

  /// Fetch all active classes
  Future<List<ClassModel>> getAllClasses() async {
    try {
      _logger.i('Fetching all active classes');

      final querySnapshot = await _firestore
          .collection(_classesCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      final classList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return ClassModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing class document ${doc.id}: $e');
              return null;
            }
          })
          .where((classModel) => classModel != null)
          .cast<ClassModel>()
          .toList();

      _logger.i('Successfully fetched ${classList.length} classes');
      return classList;
    } catch (e) {
      _logger.e('Error fetching classes: $e');
      rethrow;
    }
  }

  /// Fetch a specific class by ID
  Future<ClassModel?> getClassById(String classId) async {
    try {
      _logger.i('Fetching class by ID: $classId');

      final docSnapshot = await _firestore
          .collection(_classesCollection)
          .doc(classId)
          .get();

      if (!docSnapshot.exists) {
        _logger.w('Class not found: $classId');
        return null;
      }

      final data = docSnapshot.data()!;
      data['id'] = docSnapshot.id;
      final classModel = ClassModel.fromJson(data);

      _logger.i('Successfully fetched class: ${classModel.name}');
      return classModel;
    } catch (e) {
      _logger.e('Error fetching class by ID: $e');
      rethrow;
    }
  }

  /// Fetch classes where a specific student is enrolled
  Future<List<ClassModel>> getClassesForStudent(String studentId) async {
    try {
      _logger.i('Fetching classes for student: $studentId');

      final querySnapshot = await _firestore
          .collection(_classesCollection)
          .where('studentIds', arrayContains: studentId)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      final classList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return ClassModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing class document ${doc.id}: $e');
              return null;
            }
          })
          .where((classModel) => classModel != null)
          .cast<ClassModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${classList.length} classes for student $studentId',
      );
      return classList;
    } catch (e) {
      _logger.e('Error fetching classes for student: $e');
      rethrow;
    }
  }

  /// Fetch classes taught by a specific teacher
  Future<List<ClassModel>> getClassesForTeacher(String teacherId) async {
    try {
      _logger.i('Fetching classes for teacher: $teacherId');

      final querySnapshot = await _firestore
          .collection(_classesCollection)
          .where('teacherId', isEqualTo: teacherId)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      final classList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return ClassModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing class document ${doc.id}: $e');
              return null;
            }
          })
          .where((classModel) => classModel != null)
          .cast<ClassModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${classList.length} classes for teacher $teacherId',
      );
      return classList;
    } catch (e) {
      _logger.e('Error fetching classes for teacher: $e');
      rethrow;
    }
  }

  /// Create a new class
  Future<void> createClass(ClassModel classModel) async {
    try {
      _logger.i('Creating new class: ${classModel.name}');

      await _firestore
          .collection(_classesCollection)
          .doc(classModel.id)
          .set(classModel.toJson());

      _logger.i('Successfully created class: ${classModel.name}');
    } catch (e) {
      _logger.e('Error creating class: $e');
      rethrow;
    }
  }

  /// Update an existing class
  Future<void> updateClass(ClassModel classModel) async {
    try {
      _logger.i('Updating class: ${classModel.name}');

      await _firestore
          .collection(_classesCollection)
          .doc(classModel.id)
          .update(classModel.toJson());

      _logger.i('Successfully updated class: ${classModel.name}');
    } catch (e) {
      _logger.e('Error updating class: $e');
      rethrow;
    }
  }

  /// Delete a class (soft delete by setting isActive to false)
  Future<void> deleteClass(String classId) async {
    try {
      _logger.i('Deleting class: $classId');

      await _firestore.collection(_classesCollection).doc(classId).update({
        'isActive': false,
      });

      _logger.i('Successfully deleted class: $classId');
    } catch (e) {
      _logger.e('Error deleting class: $e');
      rethrow;
    }
  }

  /// Check if a student is enrolled in a specific class
  Future<bool> isStudentInClass(String studentId, String classId) async {
    try {
      _logger.i('Checking if student $studentId is in class $classId');

      final classModel = await getClassById(classId);
      if (classModel == null) {
        return false;
      }

      final isEnrolled = classModel.studentIds.contains(studentId);
      _logger.i(
        'Student $studentId enrollment status in class $classId: $isEnrolled',
      );
      return isEnrolled;
    } catch (e) {
      _logger.e('Error checking student enrollment: $e');
      rethrow;
    }
  }
}
